#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取当天已取消的销售出库单
"""

import logging
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient
import os

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def get_today_time_range():
    """获取当天的时间范围"""
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now  # 使用当前时间作为结束时间
    return start_time.strftime('%Y-%m-%d %H:%M:%S'), end_time.strftime('%Y-%m-%d %H:%M:%S')

def query_canceled_sales(client, start_time, end_time):
    """查询已取消的销售出库单"""
    logger = logging.getLogger(__name__)
    canceled_status = 5  # 已取消状态码
    page_size = 100  # 每页大小
    page_no = 0  # 从第0页开始
    total_canceled = 0
    all_stockouts = []  # 存储所有查询到的出库单

    logger.info(f"开始查询{start_time}至{end_time}期间已取消的销售出库单...")
    logger.debug(f"查询参数: status={canceled_status}, page_size={page_size}")

    while True:
        try:
            logger.debug(f"正在查询第{page_no + 1}页...")
            result = client.query_stockouts(
                start_consign_time=start_time,
                end_consign_time=end_time,
                status=canceled_status,
                page_no=page_no,
                page_size=page_size
            )
            
            # 调试日志：打印完整响应
            logger.info(f"API响应结构: {list(result.keys()) if isinstance(result, dict) else type(result)}")
            logger.debug(f"API完整响应: {result}")

            # 检查响应结构
            if not isinstance(result, dict):
                logger.error(f"无效的API响应格式: {type(result)}")
                break
                
            # 处理查询结果 - 根据实际响应结构解析
            stockouts = []

            # 根据实际API响应结构解析数据
            if 'content' in result:
                content = result.get('content', [])
                if isinstance(content, list):
                    stockouts = content
                elif isinstance(content, dict) and 'stockouts' in content:
                    stockouts = content.get('stockouts', [])
            elif 'stockouts' in result:
                stockouts = result.get('stockouts', [])
            elif 'data' in result:
                data = result.get('data', [])
                if isinstance(data, list):
                    stockouts = data
                elif isinstance(data, dict) and 'stockouts' in data:
                    stockouts = data.get('stockouts', [])

            count = len(stockouts)
            total_canceled += count

            # 获取总记录数（仅第一页时显示）
            if page_no == 0:
                total_records = result.get('total', 0)
                logger.info(f"API返回总记录数: {total_records}")

            if count > 0:
                # 将当前页的出库单添加到总列表
                all_stockouts.extend(stockouts)

                logger.info(f"第{page_no + 1}页查询到{count}条已取消出库单:")
                for i, stockout in enumerate(stockouts, 1):
                    stockout_no = stockout.get('stockout_no', stockout.get('order_no', '未知'))

                    # 尝试获取各种时间字段
                    time_fields = ['modified', 'update_time', 'cancel_time', 'created', 'consign_time']
                    cancel_time = '未知'
                    for field in time_fields:
                        if stockout.get(field):
                            cancel_time = stockout.get(field)
                            break

                    # 获取其他有用信息
                    receiver_name = stockout.get('receiver_name', '')
                    shop_name = stockout.get('shop_name', '')
                    logistics_no = stockout.get('logistics_no', '')
                    trade_no = stockout.get('trade_no', '')

                    logger.info(f"  {i}. 出库单号: {stockout_no}")
                    logger.info(f"     时间: {cancel_time}")
                    if trade_no:
                        logger.info(f"     订单号: {trade_no}")
                    if receiver_name:
                        logger.info(f"     收件人: {receiver_name}")
                    if shop_name:
                        logger.info(f"     店铺: {shop_name}")
                    if logistics_no:
                        logger.info(f"     物流单号: {logistics_no}")
                    logger.info("")  # 空行分隔
            else:
                logger.debug("当前页没有查询到已取消出库单")

            # 检查是否还有更多数据
            if count < page_size:
                logger.debug("已到达最后一页数据")
                break

            page_no += 1
            
        except Exception as e:
            logger.error(f"查询失败: {str(e)}", exc_info=True)
            break
    
    if total_canceled == 0:
        logger.warning(f"在{start_time}至{end_time}期间没有查询到任何已取消的销售出库单")
    else:
        logger.info(f"共查询到{total_canceled}条已取消的销售出库单")

    return all_stockouts

def test_api_permission(client):
    """测试API权限"""
    logger = logging.getLogger(__name__)
    try:
        # 测试连接
        if not client.test_connection():
            logger.error("API连接测试失败，请检查网络和配置")
            return False

        # 测试查询权限 - 使用较近的日期进行测试
        from datetime import datetime, timedelta
        test_end = datetime.now()
        test_start = test_end - timedelta(days=1)

        test_params = {
            'start_consign_time': test_start.strftime('%Y-%m-%d %H:%M:%S'),
            'end_consign_time': test_end.strftime('%Y-%m-%d %H:%M:%S'),
            'status': 5,
            'page_size': 1
        }

        logger.info(f"测试API权限，查询参数: {test_params}")
        result = client.query_stockouts(**test_params)

        # 检查API响应格式
        if not isinstance(result, dict):
            logger.error(f"API响应格式异常: {type(result)}")
            return False

        # 旺店通API成功响应应该包含flag字段
        if result.get('flag') != 'success':
            logger.error(f"API调用失败: {result.get('message', '未知错误')}")
            logger.error("请确认账号是否有查询已取消出库单的权限")
            return False

        logger.info("API权限验证成功")
        return True

    except Exception as e:
        logger.error(f"权限验证失败: {e}")
        return False

def extract_logistics_numbers(stockouts):
    """
    从已取消的销售出库单中提取物流单号

    Args:
        stockouts: 已取消的销售出库单列表

    Returns:
        dict: 包含物流单号信息的字典，格式为：
        {
            'logistics_numbers': [物流单号列表],
            'with_logistics': [有物流单号的出库单],
            'without_logistics': [没有物流单号的出库单],
            'summary': {
                'total_count': 总数,
                'with_logistics_count': 有物流单号的数量,
                'without_logistics_count': 没有物流单号的数量
            }
        }
    """
    logger = logging.getLogger(__name__)

    if not stockouts:
        logger.warning("没有出库单数据")
        return {
            'logistics_numbers': [],
            'with_logistics': [],
            'without_logistics': [],
            'summary': {
                'total_count': 0,
                'with_logistics_count': 0,
                'without_logistics_count': 0
            }
        }

    logistics_numbers = []
    with_logistics = []
    without_logistics = []

    for stockout in stockouts:
        logistics_no = stockout.get('logistics_no', '').strip()
        stockout_no = stockout.get('stockout_no', stockout.get('order_no', '未知'))

        if logistics_no:
            logistics_numbers.append(logistics_no)
            with_logistics.append({
                'stockout_no': stockout_no,
                'logistics_no': logistics_no,
                'trade_no': stockout.get('trade_no', ''),
                'shop_name': stockout.get('shop_name', ''),
                'receiver_name': stockout.get('receiver_name', ''),
                'consign_time': stockout.get('consign_time', ''),
                'cancel_time': stockout.get('modified', stockout.get('update_time', ''))
            })
        else:
            without_logistics.append({
                'stockout_no': stockout_no,
                'trade_no': stockout.get('trade_no', ''),
                'shop_name': stockout.get('shop_name', ''),
                'receiver_name': stockout.get('receiver_name', ''),
                'consign_time': stockout.get('consign_time', ''),
                'cancel_time': stockout.get('modified', stockout.get('update_time', ''))
            })

    summary = {
        'total_count': len(stockouts),
        'with_logistics_count': len(with_logistics),
        'without_logistics_count': len(without_logistics)
    }

    # 记录统计信息
    logger.info(f"物流单号提取完成:")
    logger.info(f"  总出库单数: {summary['total_count']}")
    logger.info(f"  有物流单号: {summary['with_logistics_count']}")
    logger.info(f"  无物流单号: {summary['without_logistics_count']}")

    if logistics_numbers:
        logger.info(f"  物流单号列表:")
        for i, logistics_no in enumerate(logistics_numbers, 1):
            logger.info(f"    {i}. {logistics_no}")

    return {
        'logistics_numbers': logistics_numbers,
        'with_logistics': with_logistics,
        'without_logistics': without_logistics,
        'summary': summary
    }

def export_logistics_numbers_to_file(logistics_data, filename=None):
    """
    将物流单号数据导出到文本文件

    Args:
        logistics_data: extract_logistics_numbers函数返回的数据
        filename: 输出文件名，如果为None则自动生成

    Returns:
        bool: 导出是否成功
    """
    logger = logging.getLogger(__name__)

    if not logistics_data or not logistics_data['logistics_numbers']:
        logger.warning("没有物流单号数据可导出")
        return False

    try:
        # 生成文件名
        if not filename:
            today = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"已取消出库单物流单号_{today}.txt"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("已取消销售出库单物流单号统计\n")
            f.write("=" * 50 + "\n\n")

            # 写入统计信息
            summary = logistics_data['summary']
            f.write(f"统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总出库单数: {summary['total_count']}\n")
            f.write(f"有物流单号: {summary['with_logistics_count']}\n")
            f.write(f"无物流单号: {summary['without_logistics_count']}\n\n")

            # 写入物流单号列表
            if logistics_data['logistics_numbers']:
                f.write("物流单号列表:\n")
                f.write("-" * 30 + "\n")
                for i, logistics_no in enumerate(logistics_data['logistics_numbers'], 1):
                    f.write(f"{i:3d}. {logistics_no}\n")
                f.write("\n")

            # 写入详细信息
            if logistics_data['with_logistics']:
                f.write("有物流单号的出库单详情:\n")
                f.write("-" * 50 + "\n")
                for item in logistics_data['with_logistics']:
                    f.write(f"出库单号: {item['stockout_no']}\n")
                    f.write(f"物流单号: {item['logistics_no']}\n")
                    if item['trade_no']:
                        f.write(f"订单号: {item['trade_no']}\n")
                    if item['shop_name']:
                        f.write(f"店铺: {item['shop_name']}\n")
                    if item['receiver_name']:
                        f.write(f"收件人: {item['receiver_name']}\n")
                    if item['consign_time']:
                        f.write(f"发货时间: {item['consign_time']}\n")
                    if item['cancel_time']:
                        f.write(f"取消时间: {item['cancel_time']}\n")
                    f.write("\n")

            # 写入无物流单号的出库单
            if logistics_data['without_logistics']:
                f.write("无物流单号的出库单:\n")
                f.write("-" * 30 + "\n")
                for item in logistics_data['without_logistics']:
                    f.write(f"出库单号: {item['stockout_no']}")
                    if item['trade_no']:
                        f.write(f" (订单号: {item['trade_no']})")
                    f.write("\n")

        logger.info(f"✅ 物流单号数据已导出到文件: {filename}")
        return True

    except Exception as e:
        logger.error(f"导出物流单号文件失败: {e}")
        return False

def export_to_excel(stockouts, filename=None):
    """导出已取消出库单到Excel文件"""
    logger = logging.getLogger(__name__)

    if not stockouts:
        logger.warning("没有数据可导出")
        return False

    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment, PatternFill

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "已取消销售出库单"

        # 设置表头
        headers = [
            '序号', '出库单号', '订单号', '物流单号', '店铺名称',
            '发货时间', '创建时间', '修改时间', '取消时间', '状态',
            '收件人', '收件人电话', '收件人地址', '备注'
        ]

        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

        # 写入数据
        for row, stockout in enumerate(stockouts, 2):
            # 尝试获取各种时间字段
            time_fields = ['modified', 'update_time', 'cancel_time']
            cancel_time = ''
            for field in time_fields:
                if stockout.get(field):
                    cancel_time = stockout.get(field)
                    break

            data = [
                row - 1,  # 序号
                stockout.get('stockout_no', stockout.get('order_no', '')),
                stockout.get('trade_no', ''),
                stockout.get('logistics_no', ''),
                stockout.get('shop_name', ''),
                stockout.get('consign_time', ''),
                stockout.get('created', ''),
                stockout.get('modified', ''),
                cancel_time,
                '已取消',
                stockout.get('receiver_name', ''),
                stockout.get('receiver_mobile', stockout.get('receiver_phone', '')),
                f"{stockout.get('receiver_province', '')}{stockout.get('receiver_city', '')}{stockout.get('receiver_district', '')}{stockout.get('receiver_address', '')}",
                stockout.get('remark', stockout.get('buyer_message', ''))
            ]

            for col, value in enumerate(data, 1):
                ws.cell(row=row, column=col, value=value)

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 生成文件名
        if not filename:
            today = datetime.now().strftime('%Y%m%d')
            filename = f"已取消销售出库单_{today}.xlsx"

        # 保存文件
        wb.save(filename)
        logger.info(f"✅ 已导出 {len(stockouts)} 条记录到文件: {filename}")
        return True

    except ImportError:
        logger.error("请安装openpyxl库: pip install openpyxl")
        return False
    except Exception as e:
        logger.error(f"导出Excel失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 验证API权限
        if not test_api_permission(client):
            logger.error("程序终止: API权限验证失败")
            return 1
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 查询已取消的销售出库单
        canceled_stockouts = query_canceled_sales(client, start_time, end_time)

        # 如果查询到数据，提取物流单号并提供导出选项
        if canceled_stockouts:
            # 提取物流单号
            logistics_data = extract_logistics_numbers(canceled_stockouts)

            try:
                print(f"\n查询结果汇总:")
                print(f"总出库单数: {logistics_data['summary']['total_count']}")
                print(f"有物流单号: {logistics_data['summary']['with_logistics_count']}")
                print(f"无物流单号: {logistics_data['summary']['without_logistics_count']}")

                if logistics_data['logistics_numbers']:
                    print(f"\n物流单号列表:")
                    for i, logistics_no in enumerate(logistics_data['logistics_numbers'], 1):
                        print(f"  {i}. {logistics_no}")

                # 询问导出选项
                print(f"\n导出选项:")
                print(f"1. 导出完整Excel文件 ({len(canceled_stockouts)} 条记录)")
                print(f"2. 导出物流单号文本文件 ({len(logistics_data['logistics_numbers'])} 个物流单号)")
                print(f"3. 同时导出Excel和物流单号文件")
                print(f"4. 不导出")

                choice = input("请选择导出选项 (1-4): ").strip()

                if choice == '1':
                    if export_to_excel(canceled_stockouts):
                        logger.info("Excel文件导出完成！")
                    else:
                        logger.error("Excel文件导出失败！")

                elif choice == '2':
                    if export_logistics_numbers_to_file(logistics_data):
                        logger.info("物流单号文件导出完成！")
                    else:
                        logger.error("物流单号文件导出失败！")

                elif choice == '3':
                    excel_success = export_to_excel(canceled_stockouts)
                    logistics_success = export_logistics_numbers_to_file(logistics_data)

                    if excel_success and logistics_success:
                        logger.info("所有文件导出完成！")
                    elif excel_success:
                        logger.info("Excel文件导出完成，物流单号文件导出失败！")
                    elif logistics_success:
                        logger.info("物流单号文件导出完成，Excel文件导出失败！")
                    else:
                        logger.error("所有文件导出失败！")

                elif choice == '4':
                    logger.info("用户选择不导出文件")
                else:
                    logger.warning("无效的选择，跳过导出")

            except KeyboardInterrupt:
                logger.info("\n用户取消操作")
            except Exception as e:
                logger.error(f"导出过程出错: {e}")
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == '__main__':
    main()
