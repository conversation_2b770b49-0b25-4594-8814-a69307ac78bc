#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终版本：获取当天真正已取消的销售出库单物流单号
解决了之前获取到已发货订单的问题，现在只获取真正已取消的物流单号
"""

import logging
from datetime import datetime
from wdt_post_client import WDTPostClient

def setup_logging(debug=False):
    """配置日志记录"""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def get_today_canceled_logistics(debug=False):
    """
    获取当天真正已取消的物流单号
    
    Args:
        debug: 是否显示调试信息
        
    Returns:
        dict: 包含物流单号和详细信息的字典
    """
    logger = setup_logging(debug)
    client = WDTPostClient()
    
    # 获取当天时间范围
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now
    
    start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    logger.info(f"查询当天已取消订单: {start_str} 至 {end_str}")
    
    # 第一步：查询已取消的销售订单
    logger.info("🔍 第一步：查询已取消的销售订单...")
    canceled_trades = query_canceled_trades(client, start_str, end_str, logger)
    
    if not canceled_trades:
        logger.info("✅ 当天没有已取消的销售订单")
        return create_empty_result()
    
    logger.info(f"📋 找到{len(canceled_trades)}个已取消的销售订单")
    
    # 第二步：查询对应的出库单
    logger.info("🔍 第二步：查询对应的出库单...")
    canceled_stockouts = find_canceled_stockouts(client, canceled_trades, start_str, end_str, logger, debug)
    
    # 第三步：提取物流单号
    logger.info("🔍 第三步：提取物流单号...")
    result = extract_logistics_info(canceled_stockouts, logger)
    
    return result

def query_canceled_trades(client, start_str, end_str, logger):
    """查询已取消的销售订单"""
    canceled_trades = []
    
    try:
        page_no = 0
        page_size = 100
        
        while True:
            result = client.query_sales_trades(
                start_time=start_str,
                end_time=end_str,
                trade_status=5,  # 已取消状态
                page_no=page_no,
                page_size=page_size
            )
            
            if not result or 'content' not in result:
                break
                
            trades = result.get('content', [])
            if not trades:
                break
                
            canceled_trades.extend(trades)
            logger.info(f"  第{page_no + 1}页查询到{len(trades)}个已取消订单")
            
            if len(trades) < page_size:
                break
                
            page_no += 1
            
    except Exception as e:
        logger.error(f"查询已取消销售订单失败: {e}")
        
    return canceled_trades

def find_canceled_stockouts(client, canceled_trades, start_str, end_str, logger, debug=False):
    """查找已取消订单对应的出库单"""
    
    # 提取已取消订单的订单号
    canceled_trade_nos = set()
    trade_info = {}  # 存储订单详细信息
    
    for trade in canceled_trades:
        trade_no = trade.get('trade_no', '')
        if trade_no:
            canceled_trade_nos.add(trade_no)
            trade_info[trade_no] = {
                'shop_name': trade.get('shop_name', ''),
                'receiver_name': trade.get('receiver_name', ''),
                'trade_status': trade.get('trade_status', ''),
                'created': trade.get('created', ''),
                'modified': trade.get('modified', '')
            }
    
    logger.info(f"  已取消订单号数量: {len(canceled_trade_nos)}")
    if debug and canceled_trade_nos:
        logger.debug(f"  已取消订单号: {list(canceled_trade_nos)[:5]}{'...' if len(canceled_trade_nos) > 5 else ''}")
    
    # 查询当天的出库单
    truly_canceled_stockouts = []
    all_stockouts_count = 0
    matched_stockouts_count = 0
    
    try:
        page_no = 0
        page_size = 100
        
        while True:
            result = client.query_stockouts(
                start_consign_time=start_str,
                end_consign_time=end_str,
                page_no=page_no,
                page_size=page_size
            )
            
            if not result or 'content' not in result:
                break
                
            stockouts = result.get('content', [])
            if not stockouts:
                break
            
            all_stockouts_count += len(stockouts)
            
            # 匹配已取消订单的出库单
            for stockout in stockouts:
                stockout_trade_no = stockout.get('trade_no', '')
                stockout_status = stockout.get('status')
                stockout_no = stockout.get('stockout_no', '')
                
                # 检查是否是已取消订单对应的出库单
                if stockout_trade_no in canceled_trade_nos:
                    matched_stockouts_count += 1
                    
                    if debug:
                        logger.debug(f"  找到匹配出库单: {stockout_no} - 订单: {stockout_trade_no} - 状态: {stockout_status}")
                    
                    # 检查出库单状态
                    if stockout_status == 5:  # 出库单也是已取消状态
                        truly_canceled_stockouts.append(stockout)
                        logger.info(f"  ✅ 真正已取消的出库单: {stockout_no} - 订单: {stockout_trade_no}")
                    else:
                        logger.info(f"  ⚠️  订单已取消但出库单状态为{stockout_status}: {stockout_no} - 订单: {stockout_trade_no}")
            
            if len(stockouts) < page_size:
                break
                
            page_no += 1
            
    except Exception as e:
        logger.error(f"查询出库单失败: {e}")
    
    logger.info(f"  查询到{all_stockouts_count}个出库单，其中{matched_stockouts_count}个匹配已取消订单")
    logger.info(f"  找到{len(truly_canceled_stockouts)}个真正已取消的出库单")
    
    return truly_canceled_stockouts

def extract_logistics_info(canceled_stockouts, logger):
    """提取物流单号信息"""
    logistics_numbers = []
    with_logistics = []
    without_logistics = []
    
    for stockout in canceled_stockouts:
        logistics_no = stockout.get('logistics_no', '').strip()
        stockout_no = stockout.get('stockout_no', '')
        
        detail = {
            'stockout_no': stockout_no,
            'trade_no': stockout.get('trade_no', ''),
            'logistics_no': logistics_no,
            'shop_name': stockout.get('shop_name', ''),
            'receiver_name': stockout.get('receiver_name', ''),
            'status': stockout.get('status'),
            'consign_time': stockout.get('consign_time', ''),
            'created': stockout.get('created', ''),
            'modified': stockout.get('modified', '')
        }
        
        if logistics_no:
            logistics_numbers.append(logistics_no)
            with_logistics.append(detail)
            logger.info(f"  📦 有物流单号: {stockout_no} - {logistics_no}")
        else:
            without_logistics.append(detail)
            logger.info(f"  📋 无物流单号: {stockout_no}")
    
    return {
        'logistics_numbers': logistics_numbers,
        'with_logistics': with_logistics,
        'without_logistics': without_logistics,
        'summary': {
            'total_count': len(canceled_stockouts),
            'with_logistics_count': len(with_logistics),
            'without_logistics_count': len(without_logistics)
        }
    }

def create_empty_result():
    """创建空结果"""
    return {
        'logistics_numbers': [],
        'with_logistics': [],
        'without_logistics': [],
        'summary': {
            'total_count': 0,
            'with_logistics_count': 0,
            'without_logistics_count': 0
        }
    }

def display_results(result):
    """显示结果"""
    summary = result['summary']
    
    print(f"\n" + "="*60)
    print(f"📊 当天已取消订单物流单号统计")
    print(f"="*60)
    print(f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"真正已取消出库单: {summary['total_count']} 个")
    print(f"有物流单号: {summary['with_logistics_count']} 个")
    print(f"无物流单号: {summary['without_logistics_count']} 个")
    
    if result['logistics_numbers']:
        print(f"\n🚚 需要处理的物流单号:")
        for i, item in enumerate(result['with_logistics'], 1):
            print(f"  {i:2d}. {item['logistics_no']}")
            print(f"      出库单: {item['stockout_no']}")
            print(f"      订单号: {item['trade_no']}")
            print(f"      店铺: {item['shop_name']}")
            print()
        
        # 导出到文件
        export_to_file(result)
        
    else:
        if summary['total_count'] > 0:
            print(f"\n✅ 虽然有{summary['total_count']}个已取消出库单，但都没有物流单号")
        else:
            print(f"\n✅ 当天没有真正已取消的出库单（这是好消息！）")
    
    print(f"="*60)

def export_to_file(result):
    """导出物流单号到文件"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"当天已取消物流单号_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("当天真正已取消出库单物流单号\n")
            f.write("="*50 + "\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"说明: 这些是真正已取消的出库单（订单已取消且出库单状态也为已取消）\n")
            f.write(f"物流单号数量: {len(result['logistics_numbers'])}\n\n")
            
            if result['logistics_numbers']:
                f.write("物流单号列表:\n")
                f.write("-"*30 + "\n")
                for i, logistics_no in enumerate(result['logistics_numbers'], 1):
                    f.write(f"{i:3d}. {logistics_no}\n")
                
                f.write(f"\n详细信息:\n")
                f.write("-"*50 + "\n")
                for item in result['with_logistics']:
                    f.write(f"出库单号: {item['stockout_no']}\n")
                    f.write(f"物流单号: {item['logistics_no']}\n")
                    f.write(f"订单号: {item['trade_no']}\n")
                    f.write(f"店铺: {item['shop_name']}\n")
                    f.write(f"收件人: {item['receiver_name']}\n")
                    f.write(f"状态: {item['status']} (已取消)\n")
                    f.write(f"发货时间: {item['consign_time'] or '未发货'}\n")
                    f.write(f"创建时间: {item['created']}\n")
                    f.write(f"修改时间: {item['modified']}\n")
                    f.write("\n")
        
        print(f"📄 物流单号已导出到文件: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ 导出文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 正在查询当天真正已取消的销售出库单物流单号...")
    print("📝 注意：这次查询的是真正已取消的订单，不是已发货的订单")
    
    try:
        # 询问是否显示调试信息
        debug = False
        try:
            debug_input = input("\n是否显示详细调试信息？(y/n，默认n): ").strip().lower()
            debug = debug_input in ['y', 'yes', '是']
        except:
            pass
        
        # 获取结果
        result = get_today_canceled_logistics(debug)
        
        # 显示结果
        display_results(result)
        
        # 总结
        if result['logistics_numbers']:
            print(f"\n⚠️  发现 {len(result['logistics_numbers'])} 个需要处理的物流单号！")
            print("这些是真正已取消但已分配物流单号的订单，可能需要联系物流公司处理。")
        else:
            print(f"\n✅ 太好了！当天没有需要处理的已取消物流单号。")
            
    except KeyboardInterrupt:
        print(f"\n\n用户取消操作")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    main()
