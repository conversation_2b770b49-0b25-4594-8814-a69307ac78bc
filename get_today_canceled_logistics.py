#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取当天真正已取消状态的销售出库单物流单号
专注于解决问题：只获取真正已取消（未发货）的物流单号
"""

import logging
from datetime import datetime
from wdt_post_client import WDTPostClient

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def get_today_canceled_logistics():
    """获取当天真正已取消的物流单号"""
    logger = setup_logging()
    client = WDTPostClient()
    
    # 获取当天时间范围
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now
    
    start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    logger.info(f"查询当天已取消订单: {start_str} 至 {end_str}")
    
    # 第一步：查询已取消的销售订单
    logger.info("第一步：查询已取消的销售订单...")
    canceled_trades = []
    
    try:
        page_no = 0
        page_size = 100
        
        while True:
            result = client.query_sales_trades(
                start_time=start_str,
                end_time=end_str,
                trade_status=5,  # 已取消状态
                page_no=page_no,
                page_size=page_size
            )
            
            if not result or 'content' not in result:
                break
                
            trades = result.get('content', [])
            if not trades:
                break
                
            canceled_trades.extend(trades)
            logger.info(f"第{page_no + 1}页查询到{len(trades)}个已取消订单")
            
            if len(trades) < page_size:
                break
                
            page_no += 1
            
    except Exception as e:
        logger.error(f"查询已取消销售订单失败: {e}")
        return []
    
    if not canceled_trades:
        logger.info("✅ 当天没有已取消的销售订单")
        return []
    
    logger.info(f"总共找到{len(canceled_trades)}个已取消的销售订单")
    
    # 第二步：查询当天的所有出库单，然后匹配已取消订单
    logger.info("第二步：查询当天出库单并匹配已取消订单...")
    
    # 提取已取消订单的订单号
    canceled_trade_nos = set()
    for trade in canceled_trades:
        trade_no = trade.get('trade_no', '')
        if trade_no:
            canceled_trade_nos.add(trade_no)
    
    logger.info(f"已取消订单号数量: {len(canceled_trade_nos)}")
    
    # 查询当天的出库单
    truly_canceled_stockouts = []
    
    try:
        page_no = 0
        page_size = 100
        
        while True:
            result = client.query_stockouts(
                start_consign_time=start_str,
                end_consign_time=end_str,
                page_no=page_no,
                page_size=page_size
            )
            
            if not result or 'content' not in result:
                break
                
            stockouts = result.get('content', [])
            if not stockouts:
                break
            
            # 匹配已取消订单的出库单
            for stockout in stockouts:
                stockout_trade_no = stockout.get('trade_no', '')
                stockout_status = stockout.get('status')
                consign_time = stockout.get('consign_time')
                
                # 检查是否是已取消订单对应的出库单
                if stockout_trade_no in canceled_trade_nos:
                    # 进一步检查：如果出库单状态不是已取消，说明订单取消但出库单可能已发货
                    if stockout_status == 5:  # 出库单也是已取消状态
                        truly_canceled_stockouts.append(stockout)
                        logger.debug(f"找到真正已取消的出库单: {stockout.get('stockout_no')} - 订单: {stockout_trade_no}")
                    else:
                        logger.debug(f"订单已取消但出库单状态为{stockout_status}: {stockout.get('stockout_no')}")
            
            if len(stockouts) < page_size:
                break
                
            page_no += 1
            
    except Exception as e:
        logger.error(f"查询出库单失败: {e}")
        return []
    
    logger.info(f"找到{len(truly_canceled_stockouts)}个真正已取消的出库单")
    
    # 第三步：提取物流单号
    logistics_numbers = []
    stockout_details = []
    
    for stockout in truly_canceled_stockouts:
        logistics_no = stockout.get('logistics_no', '').strip()
        stockout_no = stockout.get('stockout_no', '')
        trade_no = stockout.get('trade_no', '')
        shop_name = stockout.get('shop_name', '')
        
        detail = {
            'stockout_no': stockout_no,
            'trade_no': trade_no,
            'logistics_no': logistics_no,
            'shop_name': shop_name,
            'status': stockout.get('status'),
            'consign_time': stockout.get('consign_time', ''),
            'created': stockout.get('created', ''),
            'modified': stockout.get('modified', '')
        }
        
        stockout_details.append(detail)
        
        if logistics_no:
            logistics_numbers.append(logistics_no)
    
    # 显示结果
    print(f"\n" + "="*60)
    print(f"📊 当天已取消订单物流单号统计")
    print(f"="*60)
    print(f"查询时间: {start_str} 至 {end_str}")
    print(f"已取消销售订单: {len(canceled_trades)} 个")
    print(f"真正已取消出库单: {len(truly_canceled_stockouts)} 个")
    print(f"有物流单号的已取消出库单: {len(logistics_numbers)} 个")
    
    if logistics_numbers:
        print(f"\n🚚 需要处理的物流单号:")
        for i, logistics_no in enumerate(logistics_numbers, 1):
            # 找到对应的出库单信息
            detail = next((d for d in stockout_details if d['logistics_no'] == logistics_no), {})
            print(f"  {i:2d}. {logistics_no}")
            print(f"      出库单: {detail.get('stockout_no', '')}")
            print(f"      订单号: {detail.get('trade_no', '')}")
            print(f"      店铺: {detail.get('shop_name', '')}")
            print()
        
        # 导出到文件
        export_to_file(logistics_numbers, stockout_details)
        
    else:
        if truly_canceled_stockouts:
            print(f"\n✅ 虽然有{len(truly_canceled_stockouts)}个已取消出库单，但都没有物流单号")
        else:
            print(f"\n✅ 当天没有真正已取消的出库单（这是好消息！）")
    
    print(f"="*60)
    
    return logistics_numbers

def export_to_file(logistics_numbers, stockout_details):
    """导出物流单号到文件"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"当天已取消物流单号_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("当天真正已取消出库单物流单号\n")
            f.write("="*50 + "\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"物流单号数量: {len(logistics_numbers)}\n\n")
            
            f.write("物流单号列表:\n")
            f.write("-"*30 + "\n")
            for i, logistics_no in enumerate(logistics_numbers, 1):
                f.write(f"{i:3d}. {logistics_no}\n")
            
            f.write(f"\n详细信息:\n")
            f.write("-"*50 + "\n")
            for detail in stockout_details:
                if detail['logistics_no']:
                    f.write(f"出库单号: {detail['stockout_no']}\n")
                    f.write(f"物流单号: {detail['logistics_no']}\n")
                    f.write(f"订单号: {detail['trade_no']}\n")
                    f.write(f"店铺: {detail['shop_name']}\n")
                    f.write(f"状态: {detail['status']} (已取消)\n")
                    f.write(f"发货时间: {detail['consign_time'] or '未发货'}\n")
                    f.write(f"创建时间: {detail['created']}\n")
                    f.write(f"修改时间: {detail['modified']}\n")
                    f.write("\n")
        
        print(f"📄 物流单号已导出到文件: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ 导出文件失败: {e}")
        return False

def main():
    """主函数"""
    try:
        logistics_numbers = get_today_canceled_logistics()
        
        if logistics_numbers:
            print(f"\n⚠️  发现 {len(logistics_numbers)} 个需要处理的物流单号！")
            print("这些是真正已取消但已分配物流单号的订单，可能需要联系物流公司处理。")
        else:
            print(f"\n✅ 太好了！当天没有需要处理的已取消物流单号。")
            
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    main()
