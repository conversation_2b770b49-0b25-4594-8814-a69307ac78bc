#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用示例：如何在其他程序中获取已取消销售出库单的物流单号
"""

import logging
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient
from get_logistics_numbers import get_logistics_numbers_only, get_logistics_numbers_with_details

def example_get_today_logistics_numbers():
    """示例：获取今天的物流单号"""
    print("=" * 60)
    print("示例1：获取今天已取消出库单的物流单号")
    print("=" * 60)
    
    try:
        # 初始化客户端
        client = WDTPostClient()
        
        # 获取今天的时间范围
        now = datetime.now()
        start_time = datetime.combine(now.date(), datetime.min.time())
        end_time = now
        
        start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"查询时间范围: {start_str} 至 {end_str}")
        
        # 获取物流单号（仅返回物流单号列表）
        logistics_numbers = get_logistics_numbers_only(client, start_str, end_str)
        
        if logistics_numbers:
            print(f"找到 {len(logistics_numbers)} 个物流单号:")
            for i, logistics_no in enumerate(logistics_numbers, 1):
                print(f"  {i}. {logistics_no}")
        else:
            print("今天没有找到有物流单号的已取消出库单")
            
        return logistics_numbers
        
    except Exception as e:
        print(f"获取物流单号失败: {e}")
        return []

def example_get_recent_logistics_with_details():
    """示例：获取最近7天的物流单号及详细信息"""
    print("\n" + "=" * 60)
    print("示例2：获取最近7天已取消出库单的详细信息")
    print("=" * 60)
    
    try:
        # 初始化客户端
        client = WDTPostClient()
        
        # 获取最近7天的时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)
        
        start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"查询时间范围: {start_str} 至 {end_str}")
        
        # 获取详细信息
        logistics_data = get_logistics_numbers_with_details(client, start_str, end_str)
        
        # 显示统计信息
        summary = logistics_data['summary']
        print(f"\n统计信息:")
        print(f"  总出库单数: {summary['total_count']}")
        print(f"  有物流单号: {summary['with_logistics_count']}")
        print(f"  无物流单号: {summary['without_logistics_count']}")
        
        # 显示有物流单号的详细信息
        if logistics_data['with_logistics']:
            print(f"\n有物流单号的出库单详情:")
            for i, item in enumerate(logistics_data['with_logistics'], 1):
                print(f"  {i}. 出库单号: {item['stockout_no']}")
                print(f"     物流单号: {item['logistics_no']}")
                if item['trade_no']:
                    print(f"     订单号: {item['trade_no']}")
                if item['shop_name']:
                    print(f"     店铺: {item['shop_name']}")
                if item['receiver_name']:
                    print(f"     收件人: {item['receiver_name']}")
                print()
        
        # 显示无物流单号的出库单
        if logistics_data['without_logistics']:
            print(f"无物流单号的出库单:")
            for i, item in enumerate(logistics_data['without_logistics'], 1):
                print(f"  {i}. 出库单号: {item['stockout_no']}")
                if item['trade_no']:
                    print(f"     订单号: {item['trade_no']}")
        
        return logistics_data
        
    except Exception as e:
        print(f"获取详细信息失败: {e}")
        return None

def example_filter_logistics_numbers():
    """示例：过滤特定条件的物流单号"""
    print("\n" + "=" * 60)
    print("示例3：过滤特定店铺的物流单号")
    print("=" * 60)
    
    try:
        # 初始化客户端
        client = WDTPostClient()
        
        # 获取最近3天的时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=3)
        
        start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"查询时间范围: {start_str} 至 {end_str}")
        
        # 获取详细信息
        logistics_data = get_logistics_numbers_with_details(client, start_str, end_str)
        
        if not logistics_data['with_logistics']:
            print("没有找到有物流单号的出库单")
            return
        
        # 按店铺分组
        shop_groups = {}
        for item in logistics_data['with_logistics']:
            shop_name = item['shop_name'] or '未知店铺'
            if shop_name not in shop_groups:
                shop_groups[shop_name] = []
            shop_groups[shop_name].append(item)
        
        # 显示按店铺分组的结果
        print(f"\n按店铺分组的物流单号:")
        for shop_name, items in shop_groups.items():
            print(f"\n📍 {shop_name} ({len(items)} 个物流单号):")
            for i, item in enumerate(items, 1):
                print(f"  {i}. {item['logistics_no']} (出库单: {item['stockout_no']})")
        
        # 示例：只获取特定店铺的物流单号
        target_shop = list(shop_groups.keys())[0] if shop_groups else None
        if target_shop:
            target_logistics = [item['logistics_no'] for item in shop_groups[target_shop]]
            print(f"\n🎯 店铺 '{target_shop}' 的物流单号:")
            for logistics_no in target_logistics:
                print(f"  - {logistics_no}")
        
    except Exception as e:
        print(f"过滤物流单号失败: {e}")

def main():
    """主函数 - 运行所有示例"""
    # 配置日志
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误，减少输出
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("已取消销售出库单物流单号获取示例")
    print("=" * 60)
    
    try:
        # 示例1：获取今天的物流单号
        logistics_numbers = example_get_today_logistics_numbers()
        
        # 示例2：获取最近7天的详细信息
        logistics_data = example_get_recent_logistics_with_details()
        
        # 示例3：过滤特定条件的物流单号
        example_filter_logistics_numbers()
        
        print("\n" + "=" * 60)
        print("所有示例运行完成！")
        print("=" * 60)
        
        # 如果有数据，显示简要总结
        if logistics_numbers:
            print(f"\n📋 今天的物流单号总结:")
            print(f"共找到 {len(logistics_numbers)} 个物流单号")
            print("可以在您的程序中直接使用这些物流单号进行后续处理")
        
    except Exception as e:
        print(f"示例运行出错: {e}")

if __name__ == '__main__':
    main()
