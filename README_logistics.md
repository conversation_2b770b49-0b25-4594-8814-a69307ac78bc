# 旺店通已取消销售出库单物流单号获取工具

本工具用于获取旺店通WMS系统中已取消状态的销售出库单的物流单号。

## 功能特性

- ✅ 获取指定时间范围内已取消的销售出库单
- ✅ 提取物流单号列表
- ✅ 区分有物流单号和无物流单号的出库单
- ✅ 支持多种时间范围选择（当天、昨天、最近7天、最近30天、自定义）
- ✅ 导出物流单号到文本文件
- ✅ 导出完整数据到Excel文件
- ✅ 提供编程接口，方便集成到其他系统

## 文件说明

### 主要脚本

1. **`get_canceled_sales.py`** - 主要脚本，获取已取消的销售出库单
   - 查询已取消状态的销售出库单
   - 显示详细信息包括物流单号
   - 支持导出到Excel文件

2. **`get_logistics_numbers.py`** - 专门获取物流单号的脚本
   - 专注于物流单号提取
   - 支持多种时间范围选择
   - 提供简洁的输出格式

3. **`logistics_example.py`** - 使用示例脚本
   - 演示如何在其他程序中调用功能
   - 提供多种使用场景的示例

### 配置文件

- **`config.py`** - API配置文件
- **`wdt_post_client.py`** - 旺店通API客户端
- **`.env`** - 环境变量配置（需要自行创建）

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install requests python-dotenv openpyxl

# 创建环境变量文件
cp .env.example .env
# 编辑 .env 文件，填入您的旺店通API配置
```

### 2. 配置API信息

在 `.env` 文件中配置您的旺店通API信息：

```env
WDT_SID=your_sid
WDT_APP_KEY=your_app_key
WDT_APP_SECRET=your_app_secret
WDT_API_URL=https://openapi.wdtwms.com/open_api/service.php
```

### 3. 运行脚本

#### 获取完整的已取消出库单信息：
```bash
python get_canceled_sales.py
```

#### 专门获取物流单号：
```bash
python get_logistics_numbers.py
```

#### 查看使用示例：
```bash
python logistics_example.py
```

## 使用方法

### 方法1：直接运行脚本

运行 `get_logistics_numbers.py`，按照提示选择时间范围：

```
时间范围选项:
1. 当天 (今天00:00:00至现在)
2. 昨天 (昨天00:00:00至23:59:59)
3. 最近7天
4. 最近30天
5. 自定义时间范围

请选择时间范围 (1-5): 1
```

### 方法2：在其他程序中调用

```python
from wdt_post_client import WDTPostClient
from get_logistics_numbers import get_logistics_numbers_only, get_logistics_numbers_with_details
from datetime import datetime, timedelta

# 初始化客户端
client = WDTPostClient()

# 获取今天的物流单号列表
now = datetime.now()
start_time = datetime.combine(now.date(), datetime.min.time())
end_time = now

start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')

# 方式1：只获取物流单号列表
logistics_numbers = get_logistics_numbers_only(client, start_str, end_str)
print(f"物流单号: {logistics_numbers}")

# 方式2：获取详细信息
logistics_data = get_logistics_numbers_with_details(client, start_str, end_str)
print(f"统计: {logistics_data['summary']}")
print(f"物流单号: {logistics_data['logistics_numbers']}")
```

## 输出格式

### 控制台输出示例

```
查询结果汇总:
总出库单数: 15
有物流单号: 12
无物流单号: 3

物流单号列表:
  1. SF1234567890123
  2. YT9876543210987
  3. ZTO5555666677778
  ...
```

### 文件输出

#### 物流单号文本文件格式：
```
已取消销售出库单物流单号统计
==================================================

统计时间: 2024-01-15 14:30:25
总出库单数: 15
有物流单号: 12
无物流单号: 3

物流单号列表:
------------------------------
  1. SF1234567890123
  2. YT9876543210987
  3. ZTO5555666677778
  ...
```

#### Excel文件包含字段：
- 序号
- 出库单号
- 订单号
- 物流单号
- 店铺名称
- 发货时间
- 创建时间
- 修改时间
- 取消时间
- 状态
- 收件人
- 收件人电话
- 收件人地址
- 备注

## API接口说明

### 主要函数

#### `get_logistics_numbers_only(client, start_time, end_time)`
获取物流单号列表（简单版本）

**参数：**
- `client`: WDTPostClient实例
- `start_time`: 开始时间字符串 (YYYY-MM-DD HH:MM:SS)
- `end_time`: 结束时间字符串 (YYYY-MM-DD HH:MM:SS)

**返回：**
- `list`: 物流单号列表

#### `get_logistics_numbers_with_details(client, start_time, end_time)`
获取物流单号及详细信息（完整版本）

**参数：**
- `client`: WDTPostClient实例
- `start_time`: 开始时间字符串 (YYYY-MM-DD HH:MM:SS)
- `end_time`: 结束时间字符串 (YYYY-MM-DD HH:MM:SS)

**返回：**
```python
{
    'logistics_numbers': ['物流单号1', '物流单号2', ...],
    'with_logistics': [有物流单号的出库单详情],
    'without_logistics': [无物流单号的出库单详情],
    'summary': {
        'total_count': 总数,
        'with_logistics_count': 有物流单号的数量,
        'without_logistics_count': 无物流单号的数量
    }
}
```

## 注意事项

1. **API权限**：确保您的旺店通账号有查询销售出库单的权限
2. **时间范围**：建议单次查询时间范围不超过30天，以避免性能问题
3. **频率限制**：注意API调用频率限制，避免过于频繁的请求
4. **数据准确性**：物流单号的存在取决于出库单创建时是否已分配物流单号

## 故障排除

### 常见问题

1. **API权限验证失败**
   - 检查 `.env` 文件中的API配置是否正确
   - 确认账号是否有相应的查询权限

2. **查询不到数据**
   - 检查时间范围是否正确
   - 确认该时间段内是否确实有已取消的出库单

3. **物流单号为空**
   - 某些出库单在取消时可能还未分配物流单号
   - 这是正常情况，工具会区分有无物流单号的出库单

## 更新日志

- **v1.0** - 初始版本，支持基本的物流单号获取功能
- **v1.1** - 添加专门的物流单号提取脚本
- **v1.2** - 增加使用示例和编程接口
