#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门获取已取消销售出库单的物流单号
"""

import logging
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient
from get_canceled_sales import (
    setup_logging, 
    get_today_time_range, 
    query_canceled_sales, 
    extract_logistics_numbers,
    export_logistics_numbers_to_file,
    test_api_permission
)

def get_logistics_numbers_only(client, start_time, end_time):
    """
    获取指定时间范围内已取消销售出库单的物流单号
    支持跨天查询（自动分割为每天查询）

    Args:
        client: WDT API客户端
        start_time: 开始时间
        end_time: 结束时间

    Returns:
        list: 物流单号列表
    """
    logger = logging.getLogger(__name__)

    # 使用详细查询函数，然后只返回物流单号列表
    logistics_data = get_logistics_numbers_with_details(client, start_time, end_time)

    return logistics_data['logistics_numbers']

def split_time_range_by_days(start_time_str, end_time_str):
    """
    将时间范围按天分割，因为旺店通API限制单次查询不能超过1天

    Args:
        start_time_str: 开始时间字符串 (YYYY-MM-DD HH:MM:SS)
        end_time_str: 结束时间字符串 (YYYY-MM-DD HH:MM:SS)

    Returns:
        list: 每天的时间范围列表 [(start, end), ...]
    """
    from datetime import datetime, timedelta

    start_time = datetime.strptime(start_time_str, '%Y-%m-%d %H:%M:%S')
    end_time = datetime.strptime(end_time_str, '%Y-%m-%d %H:%M:%S')

    time_ranges = []
    current_start = start_time

    while current_start < end_time:
        # 计算当天的结束时间
        current_end = datetime.combine(current_start.date(), datetime.max.time())

        # 如果当天结束时间超过了总结束时间，使用总结束时间
        if current_end > end_time:
            current_end = end_time

        time_ranges.append((
            current_start.strftime('%Y-%m-%d %H:%M:%S'),
            current_end.strftime('%Y-%m-%d %H:%M:%S')
        ))

        # 移动到下一天的开始
        current_start = datetime.combine(current_start.date() + timedelta(days=1), datetime.min.time())

    return time_ranges

def get_logistics_numbers_with_details(client, start_time, end_time):
    """
    获取指定时间范围内已取消销售出库单的物流单号及详细信息
    支持跨天查询（自动分割为每天查询）

    Args:
        client: WDT API客户端
        start_time: 开始时间
        end_time: 结束时间

    Returns:
        dict: 包含物流单号和详细信息的字典
    """
    logger = logging.getLogger(__name__)

    # 检查时间跨度是否超过1天
    from datetime import datetime
    start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
    end_dt = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')

    if (end_dt - start_dt).days > 0:
        logger.info(f"查询时间跨度超过1天，将按天分批查询...")
        time_ranges = split_time_range_by_days(start_time, end_time)

        all_stockouts = []
        for i, (day_start, day_end) in enumerate(time_ranges, 1):
            logger.info(f"查询第{i}/{len(time_ranges)}天: {day_start} 至 {day_end}")
            day_stockouts = query_canceled_sales(client, day_start, day_end)
            if day_stockouts:
                all_stockouts.extend(day_stockouts)
                logger.info(f"第{i}天查询到{len(day_stockouts)}条记录")
            else:
                logger.info(f"第{i}天没有查询到记录")

        canceled_stockouts = all_stockouts
    else:
        # 单天查询
        canceled_stockouts = query_canceled_sales(client, start_time, end_time)

    if not canceled_stockouts:
        logger.warning("没有查询到已取消的销售出库单")
        return {
            'logistics_numbers': [],
            'with_logistics': [],
            'without_logistics': [],
            'summary': {
                'total_count': 0,
                'with_logistics_count': 0,
                'without_logistics_count': 0
            }
        }

    # 提取物流单号
    logistics_data = extract_logistics_numbers(canceled_stockouts)

    return logistics_data

def get_custom_time_range():
    """获取用户自定义的时间范围"""
    logger = logging.getLogger(__name__)
    
    try:
        print("\n请输入查询时间范围:")
        print("格式: YYYY-MM-DD HH:MM:SS")
        print("示例: 2024-01-01 00:00:00")
        
        start_input = input("开始时间: ").strip()
        end_input = input("结束时间: ").strip()
        
        # 验证时间格式
        try:
            start_time = datetime.strptime(start_input, '%Y-%m-%d %H:%M:%S')
            end_time = datetime.strptime(end_input, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            logger.error("时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式")
            return None, None
        
        # 验证时间范围
        if start_time >= end_time:
            logger.error("开始时间必须早于结束时间")
            return None, None
        
        # 检查时间范围是否过大（超过30天）
        if (end_time - start_time).days > 30:
            logger.warning("查询时间范围超过30天，可能会影响查询性能")
            confirm = input("是否继续？(y/n): ").strip().lower()
            if confirm not in ['y', 'yes', '是']:
                return None, None
        
        return start_input, end_input
        
    except KeyboardInterrupt:
        logger.info("\n用户取消操作")
        return None, None
    except Exception as e:
        logger.error(f"获取时间范围失败: {e}")
        return None, None

def main():
    """主函数"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 验证API权限
        if not test_api_permission(client):
            logger.error("程序终止: API权限验证失败")
            return 1
        
        # 选择时间范围
        print("\n时间范围选项:")
        print("1. 当天 (今天00:00:00至现在)")
        print("2. 昨天 (昨天00:00:00至23:59:59)")
        print("3. 最近7天")
        print("4. 最近30天")
        print("5. 自定义时间范围")
        
        choice = input("请选择时间范围 (1-5): ").strip()
        
        if choice == '1':
            start_time, end_time = get_today_time_range()
        elif choice == '2':
            yesterday = datetime.now() - timedelta(days=1)
            start_time = datetime.combine(yesterday.date(), datetime.min.time()).strftime('%Y-%m-%d %H:%M:%S')
            end_time = datetime.combine(yesterday.date(), datetime.max.time()).strftime('%Y-%m-%d %H:%M:%S')
        elif choice == '3':
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)
            start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
            end_time = end_time.strftime('%Y-%m-%d %H:%M:%S')
        elif choice == '4':
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
            end_time = end_time.strftime('%Y-%m-%d %H:%M:%S')
        elif choice == '5':
            start_time, end_time = get_custom_time_range()
            if not start_time or not end_time:
                logger.error("时间范围获取失败，程序退出")
                return 1
        else:
            logger.error("无效的选择，程序退出")
            return 1
        
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 获取物流单号详细信息
        logistics_data = get_logistics_numbers_with_details(client, start_time, end_time)
        
        if not logistics_data['logistics_numbers']:
            logger.info("在指定时间范围内没有找到有物流单号的已取消出库单")
            return 0
        
        # 显示结果
        print(f"\n✅ 查询完成！")
        print(f"时间范围: {start_time} 至 {end_time}")
        print(f"总出库单数: {logistics_data['summary']['total_count']}")
        print(f"有物流单号: {logistics_data['summary']['with_logistics_count']}")
        print(f"无物流单号: {logistics_data['summary']['without_logistics_count']}")
        
        print(f"\n📦 物流单号列表 ({len(logistics_data['logistics_numbers'])} 个):")
        for i, logistics_no in enumerate(logistics_data['logistics_numbers'], 1):
            print(f"  {i:3d}. {logistics_no}")
        
        # 询问是否导出
        try:
            export_choice = input(f"\n是否导出物流单号到文件？(y/n): ").strip().lower()
            if export_choice in ['y', 'yes', '是']:
                if export_logistics_numbers_to_file(logistics_data):
                    logger.info("物流单号文件导出完成！")
                else:
                    logger.error("物流单号文件导出失败！")
        except KeyboardInterrupt:
            logger.info("\n用户取消操作")
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == '__main__':
    main()
