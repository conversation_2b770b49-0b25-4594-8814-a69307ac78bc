#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取真正已取消状态的销售出库单物流单号
解决之前获取到已发货订单的问题
"""

import logging
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient
import os

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def get_today_time_range():
    """获取当天的时间范围"""
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now  # 使用当前时间作为结束时间
    return start_time.strftime('%Y-%m-%d %H:%M:%S'), end_time.strftime('%Y-%m-%d %H:%M:%S')

def query_truly_canceled_stockouts(client, start_time, end_time):
    """
    查询真正已取消的销售出库单
    使用销售订单API来查询已取消的订单，然后获取对应的出库单信息
    """
    logger = logging.getLogger(__name__)
    
    logger.info(f"开始查询{start_time}至{end_time}期间真正已取消的销售出库单...")
    
    # 方法1：使用销售订单API查询已取消订单
    logger.info("方法1：通过销售订单API查询已取消订单...")
    canceled_trades = query_canceled_trades(client, start_time, end_time)
    
    if canceled_trades:
        logger.info(f"找到{len(canceled_trades)}个已取消的销售订单")
        # 根据订单信息查找对应的出库单
        canceled_stockouts = get_stockouts_from_trades(client, canceled_trades)
        return canceled_stockouts
    
    # 方法2：查询所有出库单，然后过滤已取消且未发货的
    logger.info("方法2：查询所有出库单并过滤真正已取消的...")
    return query_canceled_stockouts_by_filter(client, start_time, end_time)

def query_canceled_trades(client, start_time, end_time):
    """查询已取消的销售订单"""
    logger = logging.getLogger(__name__)
    
    try:
        page_no = 0
        page_size = 100
        all_trades = []
        
        while True:
            result = client.query_sales_trades(
                start_time=start_time,
                end_time=end_time,
                trade_status=5,  # 已取消状态
                page_no=page_no,
                page_size=page_size
            )
            
            if not result or 'content' not in result:
                break
                
            trades = result.get('content', [])
            if not trades:
                break
                
            all_trades.extend(trades)
            logger.info(f"第{page_no + 1}页查询到{len(trades)}个已取消订单")
            
            if len(trades) < page_size:
                break
                
            page_no += 1
            
        logger.info(f"总共查询到{len(all_trades)}个已取消的销售订单")
        return all_trades
        
    except Exception as e:
        logger.error(f"查询已取消销售订单失败: {e}")
        return []

def get_stockouts_from_trades(client, trades):
    """根据销售订单获取对应的出库单"""
    logger = logging.getLogger(__name__)

    # 由于API限制，我们直接使用方法2来查询
    logger.info("由于API时间范围限制，改用方法2查询...")

    # 从已取消订单中提取有用信息
    trade_nos = [trade.get('trade_no', '') for trade in trades if trade.get('trade_no')]
    logger.info(f"已取消订单号列表: {trade_nos[:5]}{'...' if len(trade_nos) > 5 else ''}")

    # 使用方法2来查询并匹配
    return []  # 返回空，让主函数使用方法2

def query_canceled_stockouts_by_filter(client, start_time, end_time):
    """
    通过查询所有出库单并过滤的方式获取真正已取消的出库单
    """
    logger = logging.getLogger(__name__)
    
    # 扩大查询时间范围，因为已取消的出库单可能没有发货时间
    # 使用创建时间范围来查询
    start_date = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').date()
    end_date = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').date()
    
    all_canceled_stockouts = []
    
    # 按天查询，避免API限制
    current_date = start_date
    while current_date <= end_date:
        day_start = datetime.combine(current_date, datetime.min.time())
        day_end = datetime.combine(current_date, datetime.max.time())
        
        logger.info(f"查询{current_date}的出库单...")
        
        try:
            page_no = 0
            page_size = 100
            
            while True:
                # 使用当天的时间范围查询
                result = client.query_stockouts(
                    start_consign_time=day_start.strftime('%Y-%m-%d %H:%M:%S'),
                    end_consign_time=day_end.strftime('%Y-%m-%d %H:%M:%S'),
                    page_no=page_no,
                    page_size=page_size
                )
                
                if not result or 'content' not in result:
                    break
                    
                stockouts = result.get('content', [])
                if not stockouts:
                    break
                
                # 过滤真正已取消的出库单
                for stockout in stockouts:
                    status = stockout.get('status')
                    consign_time = stockout.get('consign_time')
                    logistics_no = stockout.get('logistics_no', '')
                    
                    # 判断是否为真正已取消的出库单
                    if status == 5:  # 状态为已取消
                        # 如果有物流单号但没有发货时间，可能是取消前已分配物流单号
                        # 如果有发货时间，说明已经发货了，不是真正的取消
                        if not consign_time:  # 没有发货时间才是真正的取消
                            all_canceled_stockouts.append(stockout)
                            logger.debug(f"找到真正已取消的出库单: {stockout.get('stockout_no')} - 物流单号: {logistics_no}")
                
                if len(stockouts) < page_size:
                    break
                    
                page_no += 1
                
        except Exception as e:
            logger.error(f"查询{current_date}的出库单失败: {e}")
            
        current_date += timedelta(days=1)
    
    logger.info(f"总共找到{len(all_canceled_stockouts)}个真正已取消的出库单")
    return all_canceled_stockouts

def extract_logistics_numbers_from_canceled(stockouts):
    """从已取消的出库单中提取物流单号"""
    logger = logging.getLogger(__name__)
    
    if not stockouts:
        return {
            'logistics_numbers': [],
            'with_logistics': [],
            'without_logistics': [],
            'summary': {
                'total_count': 0,
                'with_logistics_count': 0,
                'without_logistics_count': 0
            }
        }
    
    logistics_numbers = []
    with_logistics = []
    without_logistics = []
    
    for stockout in stockouts:
        logistics_no = stockout.get('logistics_no', '').strip()
        stockout_no = stockout.get('stockout_no', '未知')
        
        stockout_info = {
            'stockout_no': stockout_no,
            'logistics_no': logistics_no,
            'trade_no': stockout.get('trade_no', ''),
            'shop_name': stockout.get('shop_name', ''),
            'receiver_name': stockout.get('receiver_name', ''),
            'created_time': stockout.get('created', ''),
            'modified_time': stockout.get('modified', ''),
            'status': stockout.get('status', ''),
            'consign_time': stockout.get('consign_time', '')
        }
        
        if logistics_no:
            logistics_numbers.append(logistics_no)
            with_logistics.append(stockout_info)
        else:
            without_logistics.append(stockout_info)
    
    summary = {
        'total_count': len(stockouts),
        'with_logistics_count': len(with_logistics),
        'without_logistics_count': len(without_logistics)
    }
    
    logger.info(f"物流单号提取完成:")
    logger.info(f"  总出库单数: {summary['total_count']}")
    logger.info(f"  有物流单号: {summary['with_logistics_count']}")
    logger.info(f"  无物流单号: {summary['without_logistics_count']}")
    
    if logistics_numbers:
        logger.info(f"  物流单号列表:")
        for i, logistics_no in enumerate(logistics_numbers, 1):
            logger.info(f"    {i}. {logistics_no}")
    
    return {
        'logistics_numbers': logistics_numbers,
        'with_logistics': with_logistics,
        'without_logistics': without_logistics,
        'summary': summary
    }

def export_canceled_logistics_to_file(logistics_data, filename=None):
    """导出已取消出库单的物流单号到文件"""
    logger = logging.getLogger(__name__)
    
    if not logistics_data or not logistics_data['logistics_numbers']:
        logger.warning("没有物流单号数据可导出")
        return False
    
    try:
        if not filename:
            today = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"真正已取消出库单物流单号_{today}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("真正已取消销售出库单物流单号统计\n")
            f.write("=" * 50 + "\n\n")
            
            summary = logistics_data['summary']
            f.write(f"统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总出库单数: {summary['total_count']}\n")
            f.write(f"有物流单号: {summary['with_logistics_count']}\n")
            f.write(f"无物流单号: {summary['without_logistics_count']}\n\n")
            
            f.write("说明: 这些是真正已取消的出库单（状态为已取消且未发货）\n")
            f.write("与之前获取的已发货订单不同，这些订单确实被取消了\n\n")
            
            if logistics_data['logistics_numbers']:
                f.write("物流单号列表:\n")
                f.write("-" * 30 + "\n")
                for i, logistics_no in enumerate(logistics_data['logistics_numbers'], 1):
                    f.write(f"{i:3d}. {logistics_no}\n")
                f.write("\n")
            
            if logistics_data['with_logistics']:
                f.write("有物流单号的已取消出库单详情:\n")
                f.write("-" * 50 + "\n")
                for item in logistics_data['with_logistics']:
                    f.write(f"出库单号: {item['stockout_no']}\n")
                    f.write(f"物流单号: {item['logistics_no']}\n")
                    f.write(f"状态: {item['status']} (已取消)\n")
                    f.write(f"发货时间: {item['consign_time'] or '未发货'}\n")
                    if item['trade_no']:
                        f.write(f"订单号: {item['trade_no']}\n")
                    if item['shop_name']:
                        f.write(f"店铺: {item['shop_name']}\n")
                    if item['created_time']:
                        f.write(f"创建时间: {item['created_time']}\n")
                    if item['modified_time']:
                        f.write(f"修改时间: {item['modified_time']}\n")
                    f.write("\n")
        
        logger.info(f"✅ 真正已取消出库单物流单号数据已导出到文件: {filename}")
        return True
        
    except Exception as e:
        logger.error(f"导出文件失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 查询真正已取消的销售出库单
        canceled_stockouts = query_truly_canceled_stockouts(client, start_time, end_time)
        
        if not canceled_stockouts:
            logger.info("✅ 当天没有找到真正已取消的出库单（这是好消息！）")
            return 0
        
        # 提取物流单号
        logistics_data = extract_logistics_numbers_from_canceled(canceled_stockouts)
        
        # 显示结果
        print(f"\n✅ 查询完成！")
        print(f"时间范围: {start_time} 至 {end_time}")
        print(f"真正已取消出库单数: {logistics_data['summary']['total_count']}")
        print(f"有物流单号: {logistics_data['summary']['with_logistics_count']}")
        print(f"无物流单号: {logistics_data['summary']['without_logistics_count']}")
        
        if logistics_data['logistics_numbers']:
            print(f"\n📦 需要处理的物流单号 ({len(logistics_data['logistics_numbers'])} 个):")
            for i, logistics_no in enumerate(logistics_data['logistics_numbers'], 1):
                print(f"  {i:3d}. {logistics_no}")
            
            # 询问是否导出
            try:
                export_choice = input(f"\n是否导出物流单号到文件？(y/n): ").strip().lower()
                if export_choice in ['y', 'yes', '是']:
                    if export_canceled_logistics_to_file(logistics_data):
                        logger.info("物流单号文件导出完成！")
                    else:
                        logger.error("物流单号文件导出失败！")
            except KeyboardInterrupt:
                logger.info("\n用户取消操作")
        else:
            print("\n✅ 虽然有已取消的出库单，但都没有物流单号，无需处理")
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == '__main__':
    main()
