#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试API响应，查看出库单的真实状态
"""

from wdt_post_client import WDTPostClient
from datetime import datetime, timedelta
import json

def debug_api_response():
    """调试API响应"""
    client = WDTPostClient()
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now

    print("=== 调试旺店通出库单API响应 ===")
    print(f"查询时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')} 至 {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 先查询所有状态的出库单，看看有哪些状态
    print("\n=== 查询所有状态的出库单 ===")
    result_all = client.query_stockouts(
        start_consign_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
        end_consign_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
        page_no=0,
        page_size=10
    )

    if result_all and 'content' in result_all:
        status_counts = {}
        for item in result_all['content']:
            status = item.get('status')
            status_counts[status] = status_counts.get(status, 0) + 1

        print(f"发现的状态码统计: {status_counts}")

        # 显示各种状态的含义
        status_meanings = {
            5: '已取消',
            30: '待审核',
            95: '已发货',
            100: '已完成'
        }

        for status, count in status_counts.items():
            meaning = status_meanings.get(status, '未知状态')
            print(f"  状态 {status} ({meaning}): {count} 个")

    # 尝试不同的查询方式
    print(f"\n=== 尝试不同的查询方式 ===")

    # 方式1：不指定状态，查询所有出库单，然后在客户端过滤
    print("方式1：查询所有状态，客户端过滤")
    result_all_filter = client.query_stockouts(
        start_consign_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
        end_consign_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
        page_no=0,
        page_size=50
    )

    if result_all_filter and 'content' in result_all_filter:
        canceled_items = [item for item in result_all_filter['content'] if item.get('status') == 5]
        print(f"  客户端过滤后的已取消出库单数量: {len(canceled_items)}")

        if canceled_items:
            print("  找到的已取消出库单:")
            for i, item in enumerate(canceled_items[:3]):
                print(f"    {i+1}. {item.get('stockout_no')} - 状态:{item.get('status')} - 物流单号:{item.get('logistics_no', '无')}")

    # 方式2：尝试使用修改时间查询
    print(f"\n方式2：尝试查询昨天的数据（可能有已取消的）")
    yesterday = datetime.now() - timedelta(days=1)
    yesterday_start = datetime.combine(yesterday.date(), datetime.min.time())
    yesterday_end = datetime.combine(yesterday.date(), datetime.max.time())

    result_yesterday = client.query_stockouts(
        start_consign_time=yesterday_start.strftime('%Y-%m-%d %H:%M:%S'),
        end_consign_time=yesterday_end.strftime('%Y-%m-%d %H:%M:%S'),
        page_no=0,
        page_size=50
    )

    if result_yesterday and 'content' in result_yesterday:
        yesterday_canceled = [item for item in result_yesterday['content'] if item.get('status') == 5]
        print(f"  昨天的已取消出库单数量: {len(yesterday_canceled)}")

        if yesterday_canceled:
            print("  昨天的已取消出库单示例:")
            for i, item in enumerate(yesterday_canceled[:3]):
                print(f"    {i+1}. {item.get('stockout_no')} - 状态:{item.get('status')} - 物流单号:{item.get('logistics_no', '无')}")
                print(f"       发货时间: {item.get('consign_time', '无')}")

    # 方式3：查询状态为5的出库单（原方式）
    print(f"\n方式3：直接查询状态为5的出库单")
    result = client.query_stockouts(
        start_consign_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
        end_consign_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
        status=5,
        page_no=0,
        page_size=3
    )

    print(f"\nAPI响应结构: {list(result.keys()) if result else 'None'}")
    
    if result and 'content' in result and result['content']:
        print(f"返回记录数: {len(result['content'])}")
        print(f"总记录数: {result.get('total', 'unknown')}")
        
        for i, item in enumerate(result['content'][:3]):
            print(f"\n=== 出库单 {i+1} ===")
            print(f"出库单号: {item.get('stockout_no', '')}")
            print(f"状态: {item.get('status', '')}")
            print(f"物流单号: {item.get('logistics_no', '')}")
            print(f"发货时间: {item.get('consign_time', '')}")
            print(f"创建时间: {item.get('created', '')}")
            print(f"修改时间: {item.get('modified', '')}")
            print(f"取消时间: {item.get('cancel_time', '')}")
            print(f"店铺: {item.get('shop_name', '')}")
            
            # 显示所有可用字段
            print(f"所有字段: {sorted(item.keys())}")
            
            # 检查是否真的是已取消状态
            status = item.get('status')
            consign_time = item.get('consign_time')
            logistics_no = item.get('logistics_no')
            
            print(f"分析:")
            print(f"  - 状态码: {status} ({'已取消' if status == 5 else '其他状态'})")
            print(f"  - 是否有发货时间: {'是' if consign_time else '否'}")
            print(f"  - 是否有物流单号: {'是' if logistics_no else '否'}")
            
            if status == 5 and consign_time and logistics_no:
                print(f"  ⚠️  警告: 状态为已取消但有发货时间和物流单号，可能是已发货后取消")
            elif status == 5 and not consign_time and not logistics_no:
                print(f"  ✅ 正常: 真正的已取消订单（未发货）")
            elif status == 5 and logistics_no and not consign_time:
                print(f"  ❓ 疑问: 有物流单号但无发货时间")
    else:
        print("没有查询到数据")

if __name__ == '__main__':
    debug_api_response()
